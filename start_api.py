#!/usr/bin/env python3
"""
启动reCAPTCHA Token API服务器
"""

import uvicorn
import sys
import os

def main():
    """启动API服务器"""
    print("=" * 50)
    print("reCAPTCHA Token API 服务器")
    print("=" * 50)
    print("启动中...")
    print(f"工作目录: {os.getcwd()}")
    print("API接口:")
    print("  - GET /get-token    : 获取reCAPTCHA token")
    print("  - GET /health       : 健康检查")
    print("  - GET /             : API信息")
    print("=" * 50)
    
    try:
        # 启动服务器
        uvicorn.run(
            "api_server:app",
            host="0.0.0.0",
            port=8000,
            reload=True,  # 开发模式，代码变更时自动重载
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
