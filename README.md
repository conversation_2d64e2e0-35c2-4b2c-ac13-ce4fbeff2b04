# reCAPTCHA Token API

这是一个基于FastAPI的reCAPTCHA token获取服务，可以自动打开网页并获取reCAPTCHA token。

## 功能特点

- 🚀 基于FastAPI的高性能API服务
- 🌐 自动打开并保持网页状态
- 🔄 自动重试机制：如果获取token失败，会重新打开网页
- 🛡️ 使用undetected-chromedriver和selenium-stealth避免检测
- 📊 提供健康检查接口
- 🔧 支持热重载开发模式
- 📸 **自动截图功能**：打开网页后自动截图保存到screenshots目录
- 🗑️ **定期清理**：每24小时自动清理截图文件
- 🎛️ **手动控制**：提供手动截图和清理的API接口

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

### 方法1：使用启动脚本（推荐）
```bash
python start_api.py
```

### 方法2：直接启动
```bash
python api_server.py
```

### 方法3：使用uvicorn命令
```bash
uvicorn api_server:app --host 0.0.0.0 --port 8000 --reload
```

## API接口

服务启动后，默认运行在 `http://localhost:8000`

### 1. 获取reCAPTCHA Token
- **URL**: `GET /get-token`
- **描述**: 获取reCAPTCHA token
- **响应示例**:
```json
{
  "success": true,
  "token": "03AFcWeA7...",
  "message": "成功获取reCAPTCHA token"
}
```

### 2. 健康检查
- **URL**: `GET /health`
- **描述**: 检查服务状态（包含截图统计信息）
- **响应示例**:
```json
{
  "status": "healthy",
  "driver_active": true,
  "current_url": "https://www.genspark.ai",
  "screenshots_dir": "C:/path/to/screenshots",
  "screenshot_count": 5
}
```

### 3. 手动截图
- **URL**: `POST /screenshot`
- **描述**: 手动触发截图
- **响应示例**:
```json
{
  "success": true,
  "screenshot_path": "C:/path/to/screenshots/screenshot_20250813_143022_manual.png",
  "message": "截图成功"
}
```

### 4. 清理截图
- **URL**: `POST /cleanup-screenshots`
- **描述**: 手动清理所有截图文件
- **响应示例**:
```json
{
  "success": true,
  "deleted_count": 5,
  "remaining_count": 0,
  "message": "清理完成，删除了 5 个截图文件"
}
```

### 5. API信息
- **URL**: `GET /`
- **描述**: 获取API基本信息
- **响应示例**:
```json
{
  "message": "reCAPTCHA Token API",
  "version": "1.1.0",
  "features": [
    "自动截图保存",
    "24小时定期清理截图",
    "手动截图和清理接口"
  ],
  "endpoints": {
    "/get-token": "获取reCAPTCHA token",
    "/health": "健康检查（包含截图统计）",
    "/screenshot": "手动截图 (POST)",
    "/cleanup-screenshots": "手动清理截图 (POST)"
  }
}
```

## 测试API

运行测试脚本：
```bash
python test_api.py
```

## 工作原理

1. **初始化**: 首次调用时，系统会创建Chrome浏览器实例并打开目标网页
2. **自动截图**: 网页打开后自动截图保存到`screenshots`目录
3. **保持状态**: 浏览器保持打开状态，网页持续加载
4. **获取Token**: 调用API时执行JavaScript代码获取reCAPTCHA token
5. **自动重试**: 如果获取失败，自动重新打开网页并重试
6. **定期清理**: 后台调度器每24小时自动清理截图文件
7. **资源管理**: 应用关闭时自动清理浏览器资源

## 配置说明

### Chrome选项
- 不使用无头模式（保持网页可见）
- 启用反检测功能
- 配置窗口大小为1920x1080

### reCAPTCHA配置
- Site Key: `6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66`
- Action: `submit`
- 超时时间: 15秒

### 截图配置
- 截图目录: `screenshots/`（自动创建）
- 截图格式: PNG
- 命名规则: `screenshot_YYYYMMDD_HHMMSS_suffix.png`
- 清理周期: 每24小时自动清理
- 手动控制: 支持API手动截图和清理

## 使用示例

### Python请求示例
```python
import requests

# 获取token
response = requests.get("http://localhost:8000/get-token")
if response.status_code == 200:
    data = response.json()
    if data["success"]:
        token = data["token"]
        print(f"获取到token: {token}")
    else:
        print("获取token失败")

# 手动截图
response = requests.post("http://localhost:8000/screenshot")
if response.status_code == 200:
    data = response.json()
    print(f"截图保存到: {data['screenshot_path']}")

# 清理截图
response = requests.post("http://localhost:8000/cleanup-screenshots")
if response.status_code == 200:
    data = response.json()
    print(f"删除了 {data['deleted_count']} 个截图文件")
```

### curl请求示例
```bash
# 获取token
curl http://localhost:8000/get-token

# 健康检查
curl http://localhost:8000/health

# 手动截图
curl -X POST http://localhost:8000/screenshot

# 清理截图
curl -X POST http://localhost:8000/cleanup-screenshots
```

## 注意事项

1. **Chrome驱动**: 确保 `driver/chromedriver.exe` 存在且版本匹配
2. **网络连接**: 需要能够访问 `https://www.genspark.ai`
3. **资源占用**: 浏览器会持续运行，占用一定系统资源
4. **并发限制**: 当前版本使用单个浏览器实例，不支持高并发
5. **超时处理**: 获取token最多等待15秒，超时会返回错误

## 故障排除

### 常见问题

1. **Chrome驱动不匹配**
   - 确保chromedriver版本与Chrome浏览器版本匹配
   - 下载对应版本的chromedriver

2. **网页加载失败**
   - 检查网络连接
   - 确认目标网站可访问

3. **reCAPTCHA加载失败**
   - 等待更长时间让页面完全加载
   - 检查网站是否更新了reCAPTCHA配置

4. **端口占用**
   - 更改启动端口：`uvicorn api_server:app --port 8001`

## 开发模式

启动开发模式（代码变更自动重载）：
```bash
python start_api.py
```

## 生产部署

生产环境建议使用：
```bash
uvicorn api_server:app --host 0.0.0.0 --port 8000 --workers 1
```

注意：由于使用了浏览器实例，建议只使用单个worker进程。
