#!/usr/bin/env python3
"""
截图功能演示脚本
"""

import requests
import json
import time
import os

API_BASE_URL = "http://localhost:8000"

def check_screenshots_dir():
    """检查截图目录"""
    screenshots_dir = os.path.join(os.getcwd(), "screenshots")
    if os.path.exists(screenshots_dir):
        files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
        print(f"📁 截图目录: {screenshots_dir}")
        print(f"📸 当前截图数量: {len(files)}")
        if files:
            print("📋 截图文件列表:")
            for i, file in enumerate(files[:5], 1):  # 只显示前5个
                print(f"   {i}. {file}")
            if len(files) > 5:
                print(f"   ... 还有 {len(files) - 5} 个文件")
    else:
        print("📁 截图目录不存在")

def demo_screenshot_workflow():
    """演示完整的截图工作流程"""
    print("=" * 60)
    print("📸 截图功能演示")
    print("=" * 60)
    
    # 1. 检查初始状态
    print("\n1. 检查初始截图状态...")
    check_screenshots_dir()
    
    # 2. 获取健康状态
    print("\n2. 检查API健康状态...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"服务状态: {data.get('status')}")
            print(f"浏览器状态: {'活跃' if data.get('driver_active') else '未初始化'}")
            print(f"截图数量: {data.get('screenshot_count', 0)}")
        else:
            print("❌ 健康检查失败")
            return
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("请确保API服务器正在运行")
        return
    
    # 3. 获取token（这会触发自动截图）
    print("\n3. 获取token（触发自动截图）...")
    try:
        response = requests.get(f"{API_BASE_URL}/get-token", timeout=60)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Token获取成功")
                print("📸 网页打开时应该已经自动截图")
            else:
                print("❌ Token获取失败")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 4. 检查截图结果
    print("\n4. 检查自动截图结果...")
    check_screenshots_dir()
    
    # 5. 手动截图
    print("\n5. 执行手动截图...")
    try:
        response = requests.post(f"{API_BASE_URL}/screenshot", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ 手动截图成功")
                print(f"📸 截图路径: {data.get('screenshot_path')}")
            else:
                print("❌ 手动截图失败")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 手动截图请求失败: {e}")
    
    # 6. 再次检查截图
    print("\n6. 检查手动截图后的状态...")
    check_screenshots_dir()
    
    # 7. 演示清理功能
    print("\n7. 演示清理功能...")
    try:
        response = requests.post(f"{API_BASE_URL}/cleanup-screenshots", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                deleted = data.get("deleted_count", 0)
                remaining = data.get("remaining_count", 0)
                print(f"✅ 清理成功")
                print(f"🗑️ 删除文件数: {deleted}")
                print(f"📁 剩余文件数: {remaining}")
            else:
                print("❌ 清理失败")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 清理请求失败: {e}")
    
    # 8. 最终检查
    print("\n8. 清理后的最终状态...")
    check_screenshots_dir()
    
    print("\n" + "=" * 60)
    print("📸 截图功能演示完成！")
    print("💡 提示:")
    print("   - 网页打开时会自动截图")
    print("   - 可以通过API手动截图")
    print("   - 每24小时自动清理截图")
    print("   - 可以手动触发清理")
    print("=" * 60)

def main():
    """主函数"""
    try:
        demo_screenshot_workflow()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n\n程序执行出错: {e}")

if __name__ == "__main__":
    main()
