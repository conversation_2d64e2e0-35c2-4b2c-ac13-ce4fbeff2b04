#!/usr/bin/env python3
"""
reCAPTCHA Token API 客户端示例
演示如何调用API获取token
"""

import requests
import json
import time
from typing import Optional

class RecaptchaClient:
    """reCAPTCHA Token API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
    
    def get_token(self, timeout: int = 60) -> Optional[str]:
        """
        获取reCAPTCHA token
        
        Args:
            timeout: 请求超时时间（秒）
            
        Returns:
            str: reCAPTCHA token，失败时返回None
        """
        try:
            print("正在请求reCAPTCHA token...")
            response = requests.get(
                f"{self.base_url}/get-token",
                timeout=timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    token = data.get("token")
                    print(f"✅ 成功获取token: {token[:50]}...")
                    return token
                else:
                    print(f"❌ 获取token失败: {data.get('message', '未知错误')}")
                    return None
            else:
                print(f"❌ HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return None
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败，请确保API服务器正在运行")
            return None
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def check_health(self) -> bool:
        """
        检查API服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                status = data.get("status")
                print(f"服务状态: {status}")
                if data.get("driver_active"):
                    print(f"浏览器状态: 活跃 ({data.get('current_url', 'N/A')})")
                else:
                    print("浏览器状态: 未初始化")
                return status == "healthy"
            else:
                print(f"健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False
    
    def take_screenshot(self) -> bool:
        """
        手动截图

        Returns:
            bool: 截图是否成功
        """
        try:
            response = requests.post(f"{self.base_url}/screenshot", timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ 截图成功: {data.get('screenshot_path')}")
                    return True
                else:
                    print(f"❌ 截图失败: {data.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 截图HTTP错误 {response.status_code}: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 截图请求失败: {e}")
            return False

    def cleanup_screenshots(self) -> bool:
        """
        清理截图

        Returns:
            bool: 清理是否成功
        """
        try:
            response = requests.post(f"{self.base_url}/cleanup-screenshots", timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    deleted = data.get("deleted_count", 0)
                    remaining = data.get("remaining_count", 0)
                    print(f"✅ 清理成功: 删除了 {deleted} 个文件，剩余 {remaining} 个文件")
                    return True
                else:
                    print(f"❌ 清理失败: {data.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 清理HTTP错误 {response.status_code}: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 清理请求失败: {e}")
            return False

    def get_api_info(self) -> Optional[dict]:
        """
        获取API信息

        Returns:
            dict: API信息，失败时返回None
        """
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取API信息失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"获取API信息失败: {e}")
            return None

def main():
    """主函数 - 演示API使用"""
    print("=" * 60)
    print("reCAPTCHA Token API 客户端示例")
    print("=" * 60)
    
    # 创建客户端
    client = RecaptchaClient()
    
    # 获取API信息
    print("\n1. 获取API信息...")
    api_info = client.get_api_info()
    if api_info:
        print(f"API版本: {api_info.get('version')}")
        print(f"可用接口: {list(api_info.get('endpoints', {}).keys())}")
    
    # 检查服务健康状态
    print("\n2. 检查服务健康状态...")
    if not client.check_health():
        print("⚠️  服务不健康，但仍会尝试获取token")
    
    # 获取token
    print("\n3. 获取reCAPTCHA token...")
    token = client.get_token()
    
    if token:
        print(f"\n✅ 成功获取到token!")
        print(f"Token长度: {len(token)}")
        print(f"Token内容: {token}")

        # 演示截图功能
        print("\n4. 测试截图功能...")
        client.take_screenshot()

        # 演示清理功能
        print("\n5. 测试清理功能...")
        client.cleanup_screenshots()

        # 可以在这里使用token进行后续操作
        print("\n💡 现在可以使用这个token进行后续操作...")

    else:
        print("\n❌ 获取token失败")

    print("\n" + "=" * 60)

def demo_multiple_requests():
    """演示多次请求token"""
    print("=" * 60)
    print("多次请求演示")
    print("=" * 60)
    
    client = RecaptchaClient()
    
    for i in range(3):
        print(f"\n第 {i+1} 次请求:")
        token = client.get_token()
        if token:
            print(f"Token: {token[:30]}...")
        else:
            print("获取失败")
        
        if i < 2:  # 不是最后一次
            print("等待5秒...")
            time.sleep(5)

if __name__ == "__main__":
    # 基本使用演示
    main()
    
    # 询问是否进行多次请求演示
    print("\n是否进行多次请求演示? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes']:
            demo_multiple_requests()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except:
        pass
