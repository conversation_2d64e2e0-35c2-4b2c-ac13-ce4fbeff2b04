@echo off
echo ================================================
echo reCAPTCHA Token API 服务器启动脚本
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python版本:
python --version
echo.

REM 检查依赖是否安装
echo 检查依赖...
pip show fastapi >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo.

REM 启动API服务器
echo 启动API服务器...
echo 服务地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.
python start_api.py

pause
