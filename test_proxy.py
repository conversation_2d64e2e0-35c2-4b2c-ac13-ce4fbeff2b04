#!/usr/bin/env python3
"""
测试代理功能的脚本
演示如何设置环境变量来使用代理
"""

import os
import requests
import time

def test_with_proxy():
    """测试使用代理的情况"""
    print("=== 测试使用代理 ===")
    
    # 设置代理环境变量
    # 示例代理格式：
    # HTTP代理: http://proxy_host:proxy_port
    # HTTPS代理: https://proxy_host:proxy_port  
    # SOCKS代理: socks5://proxy_host:proxy_port
    
    # 示例：设置HTTP代理
    proxy_url = "http://127.0.0.1:7890"  # 请替换为您的实际代理地址
    os.environ['proxy'] = proxy_url
    
    print(f"设置代理环境变量: proxy={proxy_url}")
    print("现在启动API服务器将使用此代理")
    
    # 测试API调用
    test_api_calls()

def test_without_proxy():
    """测试不使用代理的情况"""
    print("\n=== 测试不使用代理 ===")
    
    # 清除代理环境变量
    if 'proxy' in os.environ:
        del os.environ['proxy']
    
    print("已清除代理环境变量")
    print("现在启动API服务器将不使用代理")
    
    # 测试API调用
    test_api_calls()

def test_api_calls():
    """测试API调用"""
    base_url = "http://localhost:8000"
    
    try:
        # 测试健康检查
        print(f"调用健康检查接口...")
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✓ 健康检查成功")
            data = response.json()
            print(f"  驱动状态: {'活跃' if data.get('driver_active') else '未初始化'}")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"✗ API调用出错: {e}")

def show_proxy_examples():
    """显示代理配置示例"""
    print("\n=== 代理配置示例 ===")
    print("支持的代理格式：")
    print("1. HTTP代理:")
    print("   export proxy=http://proxy_host:proxy_port")
    print("   例如: export proxy=http://127.0.0.1:7890")
    print()
    print("2. HTTPS代理:")
    print("   export proxy=https://proxy_host:proxy_port")
    print("   例如: export proxy=https://127.0.0.1:7890")
    print()
    print("3. SOCKS5代理:")
    print("   export proxy=socks5://proxy_host:proxy_port")
    print("   例如: export proxy=socks5://127.0.0.1:1080")
    print()
    print("4. 带认证的代理:")
    print("   export proxy=***********************************:proxy_port")
    print("   例如: export proxy=*******************************")
    print()
    print("Windows系统设置环境变量:")
    print("   set proxy=http://127.0.0.1:7890")
    print()
    print("Linux/Mac系统设置环境变量:")
    print("   export proxy=http://127.0.0.1:7890")

if __name__ == "__main__":
    print("reCAPTCHA API 代理功能测试")
    print("=" * 40)
    
    show_proxy_examples()
    
    # 检查当前代理设置
    current_proxy = os.getenv('proxy')
    if current_proxy:
        print(f"\n当前代理设置: {current_proxy}")
    else:
        print("\n当前未设置代理")
    
    print("\n注意：")
    print("1. 请先启动API服务器: python api_server.py")
    print("2. 修改test_with_proxy()函数中的代理地址为您的实际代理")
    print("3. 运行此脚本前请确保代理服务器可用")
    
    # 可以取消注释下面的行来运行测试
    # test_without_proxy()
    # test_with_proxy()
