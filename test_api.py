#!/usr/bin/env python3
"""
测试reCAPTCHA Token API接口
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_get_token():
    """测试获取token接口"""
    print("\n测试获取token接口...")
    try:
        print("正在请求token...")
        response = requests.get(f"{API_BASE_URL}/get-token", timeout=60)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data['success']}")
            print(f"消息: {data['message']}")
            if data.get('token'):
                token = data['token']
                print(f"Token (前50字符): {token[:50]}...")
                print(f"Token长度: {len(token)}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"获取token失败: {e}")
        return False

def test_screenshot():
    """测试手动截图接口"""
    print("\n测试手动截图接口...")
    try:
        response = requests.post(f"{API_BASE_URL}/screenshot", timeout=30)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data['success']}")
            print(f"截图路径: {data['screenshot_path']}")
            print(f"消息: {data['message']}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False

    except Exception as e:
        print(f"截图测试失败: {e}")
        return False

def test_cleanup():
    """测试清理截图接口"""
    print("\n测试清理截图接口...")
    try:
        response = requests.post(f"{API_BASE_URL}/cleanup-screenshots", timeout=30)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data['success']}")
            print(f"删除数量: {data['deleted_count']}")
            print(f"剩余数量: {data['remaining_count']}")
            print(f"消息: {data['message']}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False

    except Exception as e:
        print(f"清理测试失败: {e}")
        return False

def test_root():
    """测试根接口"""
    print("\n测试根接口...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"根接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("reCAPTCHA Token API 测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 测试根接口
    if not test_root():
        print("根接口测试失败，请检查服务器是否正常运行")
        return
    
    # 测试健康检查
    if not test_health():
        print("健康检查失败")
        return
    
    # 测试获取token
    token_success = test_get_token()

    # 如果token获取成功，测试截图功能
    if token_success:
        print("\n测试截图功能...")
        screenshot_success = test_screenshot()

        # 测试清理功能
        print("\n测试清理功能...")
        cleanup_success = test_cleanup()

        if screenshot_success and cleanup_success:
            print("\n✅ 所有测试通过！")
        else:
            print("\n⚠️  部分测试失败，但核心功能正常")
    else:
        print("\n❌ token获取测试失败")

    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
